package com.cdkit.modules.cm.api.budget.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 年度预算保存请求对象
 * <AUTHOR>
 * @date 2025-08-04
 */
@Schema(description = "年度预算保存请求对象")
@Data
public class CostAnnualBudgetSaveRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    // ==================== 年度总预算主表信息 ====================
    
    /**UUID主键*/
    @Schema(description = "UUID主键")
    private String id;

    /**总预算编号(ZYS+4位年份+3位流水)*/
    @Schema(description = "总预算编号(ZYS+4位年份+3位流水)")
    private String budgetCode;

    /**总预算名称*/
    @Schema(description = "总预算名称")
    private String budgetName;

    /**年份*/
    @Schema(description = "年份")
    private String budgetYear;

    /**版本号*/
    @Schema(description = "版本号")
    private String version;

    /**状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/CHANGED-已变更)*/
    @Schema(description = "状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/CHANGED-已变更)")
    private String budgetStatus;

    /**所属单位*/
    @Schema(description = "所属单位")
    private String professionalCompany;

    /**收入总金额(万元)*/
    @Schema(description = "收入总金额(万元)")
    private BigDecimal revenueTotalAmount;

    /**收入已使用金额(万元)*/
    @Schema(description = "收入已使用金额(万元)")
    private BigDecimal revenueUsedAmount;

    /**收入剩余金额(万元)*/
    @Schema(description = "收入剩余金额(万元)")
    private BigDecimal revenueRemainingAmount;

    /**收入总金额差异额(万元)*/
    @Schema(description = "收入总金额差异额(万元)")
    private BigDecimal revenueDifferenceAmount;

    /**直接成本总金额(万元)*/
    @Schema(description = "直接成本总金额(万元)")
    private BigDecimal directCostTotalAmount;

    /**直接成本已使用金额(万元)*/
    @Schema(description = "直接成本已使用金额(万元)")
    private BigDecimal directCostUsedAmount;

    /**直接成本剩余金额(万元)*/
    @Schema(description = "直接成本剩余金额(万元)")
    private BigDecimal directCostRemainingAmount;

    /**直接成本总金额差异额(万元)*/
    @Schema(description = "直接成本总金额差异额(万元)")
    private BigDecimal directCostDifferenceAmount;

    /**备注*/
    @Schema(description = "备注")
    private String remark;

    /**父预算ID(变更时关联原预算)*/
    @Schema(description = "父预算ID(变更时关联原预算)")
    private String parentBudgetId;

    /**变更原因*/
    @Schema(description = "变更原因")
    private String changeReason;

    /**附件URL*/
    @Schema(description = "附件URL")
    private String attachmentUrl;

    // ==================== 项目年度预算明细列表 ====================
    
    /**项目年度预算明细列表*/
    @Schema(description = "项目年度预算明细列表")
    private List<CostAnnualBudgetDetailRequest> budgetDetailList;

    /**
     * 项目年度预算明细请求对象
     */
    @Schema(description = "项目年度预算明细请求对象")
    @Data
    public static class CostAnnualBudgetDetailRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        /**UUID主键*/
        @Schema(description = "UUID主键")
        private String id;

        /**关联预算主表ID*/
        @Schema(description = "关联预算主表ID")
        private String budgetId;

        /**关联项目年度计划ID*/
        @Schema(description = "关联项目年度计划ID")
        private String planId;


        /**项目编号*/
        @Schema(description = "项目编号")
        private String projectCode;

        /**年度预算项目名称*/
        @Schema(description = "年度预算项目名称")
        private String projectName;

        /**所属单位*/
        @Schema(description = "所属单位")
        private String professionalCompany;

        /**下属中心*/
        @Schema(description = "下属中心")
        private String center;

        /**预算类型*/
        @Schema(description = "预算类型")
        private String budgetType;

        /**WBS编号*/
        @Schema(description = "WBS编号")
        private String wbsCode;

        /**项目类型*/
        @Schema(description = "项目类型")
        private String projectType;

        /**四级业务*/
        @Schema(description = "四级业务")
        private String fourthLevelBusiness;

        /**业务小类*/
        @Schema(description = "业务小类")
        private String businessSubcategory;

        /**收入预算(万元)*/
        @Schema(description = "收入预算(万元)")
        private BigDecimal revenueBudget;

        /**直接成本预算(万元)*/
        @Schema(description = "直接成本预算(万元)")
        private BigDecimal directCostBudget;

        /**其他成本预算(万元)*/
        @Schema(description = "其他成本预算(万元)")
        private BigDecimal otherCostBudget;

        /**利润预算(万元)*/
        @Schema(description = "利润预算(万元)")
        private BigDecimal profitBudget;

        /**直接成本明细列表*/
        @Schema(description = "直接成本明细列表")
        private List<CostAnnualBudgetDetailDirectCostRequest> directCostList;
    }

    /**
     * 直接成本明细请求对象
     */
    @Schema(description = "直接成本明细请求对象")
    @Data
    public static class CostAnnualBudgetDetailDirectCostRequest implements Serializable {
        private static final long serialVersionUID = 1L;

        /**UUID主键*/
        @Schema(description = "UUID主键")
        private String id;

        /**关联年度预算明细ID*/
        @Schema(description = "关联年度预算明细ID")
        private String budgetDetailId;

        /**费用科目*/
        @Schema(description = "费用科目")
        private String costItem;

        /**科目释义*/
        @Schema(description = "科目释义")
        private String costDescription;

        /**支出预算金额(万元)*/
        @Schema(description = "支出预算金额(万元)")
        private BigDecimal costAmount;
    }
}
