package com.cdkit.modules.cm.application.schedule;

import com.cdkit.config.mybatis.TenantIgnoreContext;
import com.cdkit.modules.cm.domain.businessdata.model.entity.CostMaterialPriceEntity;
import com.cdkit.modules.cm.domain.businessdata.repository.CostMaterialPriceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 材料单价定时任务
 * <AUTHOR>
 * @date 2025/07/16
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class MaterialPriceScheduleTask {

    private final CostMaterialPriceRepository costMaterialPriceRepository;

    /**
     * 定时更新材料单价状态
     * 每天早上8点执行，根据生效失效日期更新状态
     */
    @Scheduled(cron = "0 0 8 * * ?")
    public void updateMaterialPriceStatus() {
        log.info("开始执行材料单价状态更新定时任务");
        //忽略租户
        TenantIgnoreContext.setTenantIgnore(true);
        try {
            // 查询需要更新状态的记录
            List<CostMaterialPriceEntity> needUpdateList = costMaterialPriceRepository.findNeedUpdateStatus();
            
            if (needUpdateList.isEmpty()) {
                log.info("没有需要更新状态的材料单价记录");
                return;
            }
            
            int updateCount = 0;
            for (CostMaterialPriceEntity entity : needUpdateList) {
                try {
                    // 更新状态
                    entity.updateStatus();
                    costMaterialPriceRepository.updateById(entity);
                    updateCount++;
                    
                    log.debug("更新材料单价状态：ID={}, 物料名称={}, 新状态={}", 
                            entity.getId(), entity.getMaterialName(), entity.getMaterialPriceStatus());
                } catch (Exception e) {
                    log.error("更新材料单价状态失败：ID={}, 物料名称={}", 
                            entity.getId(), entity.getMaterialName(), e);
                }
            }
            
            log.info("材料单价状态更新定时任务执行完成，共更新{}条记录", updateCount);
            
        } catch (Exception e) {
            log.error("材料单价状态更新定时任务执行失败", e);
        }
    }
}
