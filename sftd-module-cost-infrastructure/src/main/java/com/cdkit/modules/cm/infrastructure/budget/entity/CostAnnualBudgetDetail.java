package com.cdkit.modules.cm.infrastructure.budget.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * @Description: 项目年度预算
 * @Author: sunhzh
 * @Date:   2025-07-30
 * @Version: V1.0
 */
@Schema(description="cost_annual_budget_detail对象")
@Data
@TableName("cost_annual_budget_detail")
public class CostAnnualBudgetDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**UUID主键*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "UUID主键")
    private String id;
	/**关联预算主表ID*/
    @Schema(description = "关联预算主表ID")
    private String budgetId;
	/**关联项目年度计划ID*/
	@Excel(name = "关联项目年度计划ID", width = 15)
    @Schema(description = "关联项目年度计划ID")
    private String planId;
	/**项目编号*/
	@Excel(name = "项目编号", width = 15)
    @Schema(description = "项目编号")
    private String projectCode;
	/**年度预算项目名称*/
	@Excel(name = "年度预算项目名称", width = 15)
    @Schema(description = "年度预算项目名称")
    private String projectName;
	/**所属单位*/
	@Excel(name = "所属单位", width = 15)
    @Schema(description = "所属单位")
    private String professionalCompany;
	/**下属中心*/
	@Excel(name = "下属中心", width = 15)
    @Schema(description = "下属中心")
    private String center;
	/**预算类型*/
	@Excel(name = "预算类型", width = 15)
    @Schema(description = "预算类型")
    private String budgetType;
	/**WBS编号*/
	@Excel(name = "WBS编号", width = 15)
    @Schema(description = "WBS编号")
    private String wbsCode;
	/**项目类型*/
	@Excel(name = "项目类型", width = 15)
    @Schema(description = "项目类型")
    private String projectType;
	/**四级业务*/
	@Excel(name = "四级业务", width = 15)
    @Schema(description = "四级业务")
    private String fourthLevelBusiness;
	/**业务小类*/
	@Excel(name = "业务小类", width = 15)
    @Schema(description = "业务小类")
    private String businessArea;
	/**收入预算金额(万元)*/
	@Excel(name = "收入预算金额(万元)", width = 15)
    @Schema(description = "收入预算金额(万元)")
    private java.math.BigDecimal revenueBudget;
	/**直接成本预算(万元)*/
	@Excel(name = "直接成本预算(万元)", width = 15)
    @Schema(description = "直接成本预算(万元)")
    private java.math.BigDecimal directCostBudget;
	/**其他成本预算(万元)*/
	@Excel(name = "其他成本预算(万元)", width = 15)
    @Schema(description = "其他成本预算(万元)")
    private java.math.BigDecimal otherCostBudget;
	/**利润预算(万元)*/
	@Excel(name = "利润预算(万元)", width = 15)
    @Schema(description = "利润预算(万元)")
    private java.math.BigDecimal profitBudget;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**租户ID*/
	@Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;
	/**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;
}
