package com.cdkit.modules.cm.infrastructure.budget.listener;

import com.cdkit.modules.cm.domain.budget.event.BudgetDetailSaveEvent;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import com.cdkit.modules.cm.infrastructure.budget.adapter.AnnualBudgetDetailSaveAdapter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 年度预算明细保存事件监听器
 * 在基础设施层监听应用层发布的事件，执行实际的数据库保存操作
 * <AUTHOR>
 * @date 2025-08-04
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class BudgetDetailSaveEventListener {

    private final AnnualBudgetDetailSaveAdapter annualBudgetDetailSaveAdapter;

    /**
     * 监听年度预算明细保存事件
     */
    @EventListener
    public void handleBudgetDetailSaveEvent(BudgetDetailSaveEvent event) {
        log.info("收到年度预算明细保存事件，budgetId: {}, 明细数量: {}", 
                event.getBudgetId(), 
                event.getBudgetDetailList() != null ? event.getBudgetDetailList().size() : 0);

        try {
            // 转换领域对象为适配器数据对象
            List<AnnualBudgetDetailSaveAdapter.BudgetDetailData> detailDataList = 
                    convertToBudgetDetailDataList(event.getBudgetDetailList());
            
            // 调用基础设施层适配器保存数据
            annualBudgetDetailSaveAdapter.saveDetailsToDatabase(event.getBudgetId(), detailDataList);
            
            log.info("年度预算明细保存事件处理成功，budgetId: {}", event.getBudgetId());
            
        } catch (Exception e) {
            log.error("年度预算明细保存事件处理失败，budgetId: {}", event.getBudgetId(), e);
            // 这里可以根据需要进行错误处理，比如重试、发送告警等
            throw new RuntimeException("保存明细数据失败：" + e.getMessage(), e);
        }
    }

    /**
     * 转换领域对象为适配器数据对象
     */
    private List<AnnualBudgetDetailSaveAdapter.BudgetDetailData> convertToBudgetDetailDataList(
            List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        
        if (budgetDetailList == null || budgetDetailList.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<AnnualBudgetDetailSaveAdapter.BudgetDetailData> detailDataList = new ArrayList<>();
        
        for (CostAnnualBudgetEntity.BudgetDetailInfo detailInfo : budgetDetailList) {
            AnnualBudgetDetailSaveAdapter.BudgetDetailData detailData = new AnnualBudgetDetailSaveAdapter.BudgetDetailData();
            
            // 设置ID，如果为空则生成新的
            if (detailInfo.getId() == null || detailInfo.getId().trim().isEmpty()) {
                detailData.setId(UUID.randomUUID().toString());
            } else {
                detailData.setId(detailInfo.getId());
            }
            
            // 复制基本属性
            detailData.setPlanId(detailInfo.getPlanId());
            detailData.setProjectCode(detailInfo.getProjectCode());
            detailData.setProjectName(detailInfo.getProjectName());
            detailData.setProfessionalCompany(detailInfo.getProfessionalCompany());
            detailData.setCenter(detailInfo.getCenter());
            detailData.setBudgetType(detailInfo.getBudgetType());
            detailData.setWbsCode(detailInfo.getWbsCode());
            detailData.setProjectType(detailInfo.getProjectType());
            detailData.setFourthLevelBusiness(detailInfo.getFourthLevelBusiness());
            detailData.setBusinessSubcategory(detailInfo.getBusinessSubcategory());
            detailData.setRevenueBudget(detailInfo.getRevenueBudget());
            detailData.setDirectCostBudget(detailInfo.getDirectCostBudget());
            detailData.setOtherCostBudget(detailInfo.getOtherCostBudget());
            detailData.setProfitBudget(detailInfo.getProfitBudget());
            
            // 转换直接成本明细
            if (detailInfo.getDirectCostList() != null && !detailInfo.getDirectCostList().isEmpty()) {
                List<AnnualBudgetDetailSaveAdapter.DirectCostData> directCostDataList = new ArrayList<>();
                
                for (CostAnnualBudgetEntity.DirectCostInfo directCostInfo : detailInfo.getDirectCostList()) {
                    AnnualBudgetDetailSaveAdapter.DirectCostData directCostData = new AnnualBudgetDetailSaveAdapter.DirectCostData();
                    
                    // 设置ID，如果为空则生成新的
                    if (directCostInfo.getId() == null || directCostInfo.getId().trim().isEmpty()) {
                        directCostData.setId(UUID.randomUUID().toString());
                    } else {
                        directCostData.setId(directCostInfo.getId());
                    }
                    
                    directCostData.setCostItem(directCostInfo.getCostItem());
                    directCostData.setCostDescription(directCostInfo.getCostDescription());
                    directCostData.setCostAmount(directCostInfo.getCostAmount());
                    
                    directCostDataList.add(directCostData);
                }
                
                detailData.setDirectCostList(directCostDataList);
            }
            
            detailDataList.add(detailData);
        }
        
        return detailDataList;
    }
}
