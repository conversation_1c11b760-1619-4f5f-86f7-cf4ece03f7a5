package com.cdkit.modules.cm.infrastructure.businessdata.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.businessdata.model.entity.CostMaterialPriceEntity;
import com.cdkit.modules.cm.domain.businessdata.repository.CostMaterialPriceRepository;
import com.cdkit.modules.cm.domain.businessdata.valobj.MaterialPriceStatusEnum;
import com.cdkit.modules.cm.infrastructure.businessdata.CostMaterialPriceInfraConverter;
import com.cdkit.modules.cm.infrastructure.businessdata.entity.CostMaterialPrice;
import com.cdkit.modules.cm.infrastructure.businessdata.mapper.CostMaterialPriceMapper;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 材料单价仓储实现
 * <AUTHOR>
 * @date 2025/07/16
 */
@Repository
@RequiredArgsConstructor
public class CostMaterialPriceRepositoryImpl implements CostMaterialPriceRepository {
    @Resource
    CostMaterialPriceMapper costMaterialPriceMapper;

    @Override
    public CostMaterialPriceEntity save(CostMaterialPriceEntity entity) {
        CostMaterialPrice po = CostMaterialPriceInfraConverter.convert(entity);
        costMaterialPriceMapper.insert(po);
        return CostMaterialPriceInfraConverter.convert(po);
    }

    @Override
    public CostMaterialPriceEntity updateById(CostMaterialPriceEntity entity) {
        CostMaterialPrice po = CostMaterialPriceInfraConverter.convert(entity);
        costMaterialPriceMapper.updateById(po);
        return CostMaterialPriceInfraConverter.convert(po);
    }

    @Override
    public void deleteById(String id) {
        costMaterialPriceMapper.deleteById(id);
    }

    @Override
    public void deleteByIds(List<String> ids) {
        costMaterialPriceMapper.deleteBatchIds(ids);
    }

    @Override
    public CostMaterialPriceEntity findById(String id) {
        CostMaterialPrice po = costMaterialPriceMapper.selectById(id);
        return CostMaterialPriceInfraConverter.convert(po);
    }

    @Override
    public List<CostMaterialPriceEntity> findByIds(List<String> ids) {
        List<CostMaterialPrice> poList = costMaterialPriceMapper.selectBatchIds(ids);
        return CostMaterialPriceInfraConverter.convertList(poList);
    }

    @Override
    public PageRes<CostMaterialPriceEntity> queryPageList(CostMaterialPriceEntity queryEntity, PageReq pageReq) {
        LambdaQueryWrapper<CostMaterialPrice> queryWrapper = buildQueryWrapper(queryEntity);

        Page<CostMaterialPrice> page = new Page<>(pageReq.getCurrent(), pageReq.getSize());
        IPage<CostMaterialPrice> pageResult = costMaterialPriceMapper.selectPage(page, queryWrapper);

        List<CostMaterialPriceEntity> entityList = CostMaterialPriceInfraConverter.convertList(pageResult.getRecords());

        return PageRes.of(pageReq.getCurrent(), pageReq.getSize(), entityList, pageResult.getTotal(), pageResult.getPages());
    }

    @Override
    public List<CostMaterialPriceEntity> queryList(CostMaterialPriceEntity queryEntity) {
        LambdaQueryWrapper<CostMaterialPrice> queryWrapper = buildQueryWrapper(queryEntity);

        List<CostMaterialPrice> poList = costMaterialPriceMapper.selectList(queryWrapper);
        return CostMaterialPriceInfraConverter.convertList(poList);
    }

    @Override
    public CostMaterialPriceEntity getLatestByMaterialCode(String materialCode) {
        if (!StringUtils.hasText(materialCode)) {
            return null;
        }
        
        LambdaQueryWrapper<CostMaterialPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostMaterialPrice::getMaterialCode, materialCode)
                .orderByDesc(CostMaterialPrice::getCreateTime)
                .last("LIMIT 1");
        
        CostMaterialPrice po = costMaterialPriceMapper.selectOne(queryWrapper);
        return CostMaterialPriceInfraConverter.convert(po);
    }

    @Override
    public List<CostMaterialPriceEntity> findEffectiveInPeriod(String materialCode, Date startDate, Date endDate) {
        if (!StringUtils.hasText(materialCode) || startDate == null || endDate == null) {
            return List.of();
        }

        LambdaQueryWrapper<CostMaterialPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostMaterialPrice::getMaterialCode, materialCode)
                .eq(CostMaterialPrice::getMaterialPriceStatus, MaterialPriceStatusEnum.IN_EFFECT.getCode())
                .le(CostMaterialPrice::getEffectiveDate, endDate)
                .and(wrapper -> wrapper
                        // 失效日期为空（一直生效）或失效日期大于等于查询开始日期
                        .isNull(CostMaterialPrice::getExpirationDate)
                        .or()
                        .ge(CostMaterialPrice::getExpirationDate, startDate)
                );

        List<CostMaterialPrice> poList = costMaterialPriceMapper.selectList(queryWrapper);
        return CostMaterialPriceInfraConverter.convertList(poList);
    }

    @Override
    public List<CostMaterialPriceEntity> findCurrentEffective(String materialCode) {
        if (!StringUtils.hasText(materialCode)) {
            return List.of();
        }
        
        Date now = new Date();
        LambdaQueryWrapper<CostMaterialPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostMaterialPrice::getMaterialCode, materialCode)
                .eq(CostMaterialPrice::getMaterialPriceStatus, MaterialPriceStatusEnum.IN_EFFECT.getCode())
                .le(CostMaterialPrice::getEffectiveDate, now)
                .ge(CostMaterialPrice::getExpirationDate, now);
        
        List<CostMaterialPrice> poList = costMaterialPriceMapper.selectList(queryWrapper);
        return CostMaterialPriceInfraConverter.convertList(poList);
    }

    @Override
    public void batchSetExpired(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return;
        }
        
        LambdaUpdateWrapper<CostMaterialPrice> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CostMaterialPrice::getId, ids)
                .set(CostMaterialPrice::getMaterialPriceStatus, MaterialPriceStatusEnum.EXPIRED.getCode())
                .set(CostMaterialPrice::getUpdateTime, new Date());
        
        costMaterialPriceMapper.update(null, updateWrapper);
    }

    @Override
    public List<CostMaterialPriceEntity> findByStatus(String status) {
        if (!StringUtils.hasText(status)) {
            return List.of();
        }
        
        LambdaQueryWrapper<CostMaterialPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostMaterialPrice::getMaterialPriceStatus, status);
        
        List<CostMaterialPrice> poList = costMaterialPriceMapper.selectList(queryWrapper);
        return CostMaterialPriceInfraConverter.convertList(poList);
    }

    @Override
    public List<CostMaterialPriceEntity> findNeedUpdateStatus() {
        Date now = new Date();

        LambdaQueryWrapper<CostMaterialPrice> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(CostMaterialPrice::getMaterialPriceStatus, MaterialPriceStatusEnum.PENDING_SUBMIT.getCode())
                .and(wrapper -> wrapper
                        // 未生效状态且已到生效日期的记录
                        .and(w -> w.eq(CostMaterialPrice::getMaterialPriceStatus, MaterialPriceStatusEnum.NOT_EFFECTIVE.getCode())
                                .le(CostMaterialPrice::getEffectiveDate, now))
                        // 生效中状态且有失效日期且已超过失效日期的记录（失效日期为空的不处理）
                        .or(w -> w.eq(CostMaterialPrice::getMaterialPriceStatus, MaterialPriceStatusEnum.IN_EFFECT.getCode())
                                .isNotNull(CostMaterialPrice::getExpirationDate)
                                .lt(CostMaterialPrice::getExpirationDate, now))
                );

        List<CostMaterialPrice> poList = costMaterialPriceMapper.selectList(queryWrapper);
        return CostMaterialPriceInfraConverter.convertList(poList);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<CostMaterialPrice> buildQueryWrapper(CostMaterialPriceEntity queryEntity) {
        LambdaQueryWrapper<CostMaterialPrice> queryWrapper = new LambdaQueryWrapper<>();
        
        if (queryEntity != null) {
            if (StringUtils.hasText(queryEntity.getMaterialName())) {
                queryWrapper.like(CostMaterialPrice::getMaterialName, queryEntity.getMaterialName());
            }
            if (StringUtils.hasText(queryEntity.getMaterialCode())) {
                queryWrapper.like(CostMaterialPrice::getMaterialCode, queryEntity.getMaterialCode());
            }
            if (StringUtils.hasText(queryEntity.getMaterialPriceStatus())) {
                queryWrapper.eq(CostMaterialPrice::getMaterialPriceStatus, queryEntity.getMaterialPriceStatus());
            }
            if (StringUtils.hasText(queryEntity.getPriceType())) {
                queryWrapper.eq(CostMaterialPrice::getPriceType, queryEntity.getPriceType());
            }
            if (StringUtils.hasText(queryEntity.getSource())) {
                queryWrapper.like(CostMaterialPrice::getSource, queryEntity.getSource());
            }
        }
        
        queryWrapper.orderByDesc(CostMaterialPrice::getCreateTime);
        return queryWrapper;
    }


}
