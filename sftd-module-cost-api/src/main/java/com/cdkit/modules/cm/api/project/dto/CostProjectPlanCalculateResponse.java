package com.cdkit.modules.cm.api.project.dto;

import com.cdkitframework.poi.excel.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 项目计划计算响应DTO
 * @Author: sunhzh
 * @Date: 2025-07-18
 * @Version: V1.0
 */
@Schema(description = "项目计划计算响应DTO")
@Data
public class CostProjectPlanCalculateResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**计划ID*/
    @Schema(description = "计划ID")
    private String planId;

    /**计划名称*/
    @Schema(description = "计划名称")
    private String planName;

    /**项目名称*/
    @Schema(description = "项目名称")
    private String projectName;

    /**直接成本小计*/
    @Schema(description = "直接成本小计")
    private BigDecimal directCostTotal;

    /**其他成本小计*/
    @Schema(description = "其他成本小计")
    private BigDecimal otherCostTotal;

    /**税金及附加小计*/
    @Schema(description = "税金及附加小计")
    private BigDecimal taxCostTotal;

    /**成本总计*/
    @Schema(description = "成本总计")
    private BigDecimal costTotal;

    /**项目利润(万元)*/
    @Schema(description = "项目利润(万元)")
    private BigDecimal projectProfit;

    /**利润率(%)*/
    @Schema(description = "利润率(%)")
    private BigDecimal profitMargin;

    /**年度预算应收总计(万元)*/
    @Schema(description = "年度预算应收总计(万元)")
    private BigDecimal totalRevenue;

    /**合同/预估收入（税后万元）*/
    @Schema(description = "合同/预估收入（税后万元）")
    private BigDecimal contractEstimatedIncomeAfterTax;

    /**计算后的项目计划明细列表*/
    @Schema(description = "计算后的项目计划明细列表")
    private List<DetailDTO> detailList;

    /**直接成本明细列表*/
    @Schema(description = "直接成本明细列表")
    private List<DirectCostDTO> directCostList;

    /**其他成本明细列表*/
    @Schema(description = "其他成本明细列表")
    private List<OtherCostDTO> otherCostList;

    /**税金及附加明细列表*/
    @Schema(description = "税金及附加明细列表")
    private List<TaxCostDTO> taxCostList;

    /**原料明细汇总*/
    @Schema(description = "原料明细汇总")
    private List<MaterialSummaryDTO> materialSummaryList;

    /**
     * 项目计划明细DTO
     */
    @Schema(description = "项目计划明细DTO")
    @Data
    public static class DetailDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**产品型号*/
        @Schema(description = "产品型号")
        private String productModel;

        /**密度*/
        @Schema(description = "密度")
        private BigDecimal density;

        /**用量*/
        @Schema(description = "用量")
        private BigDecimal usageAmount;

        /**年度预算需求吨*/
        @Schema(description = "年度预算需求吨")
        private BigDecimal annualBudgetDemand;

        /**预计年处理量（油，单位方）*/
        @Schema(description = "预计年处理量（油，单位方）")
        private BigDecimal estimatedOilProcessing;

        /**费率（油）*/
        @Schema(description = "费率（油）")
        private BigDecimal oilRate;

        /**年度预算应收油（万元）*/
        @Schema(description = "年度预算应收油（万元）")
        private BigDecimal revenueOil;

        /**预计年处理量（水，单位方）*/
        @Schema(description = "预计年处理量（水，单位方）")
        private BigDecimal estimatedWaterProcessing;

        /**费率（水）*/
        @Schema(description = "费率（水）")
        private BigDecimal waterRate;

        /**年度预算应收水（万元）*/
        @Schema(description = "年度预算应收水（万元）")
        private BigDecimal revenueWater;
    }

    /**
     * 直接成本明细DTO
     */
    @Schema(description = "直接成本明细DTO")
    @Data
    public static class DirectCostDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**产品名称*/
        @Schema(description = "产品名称")
        private String productName;

        /**预计用量*/
        @Schema(description = "预计用量")
        private BigDecimal estimatedUsage;

        /**配方名称*/
        @Schema(description = "配方名称")
        private String formulaName;

        /**配方编号*/
        @Schema(description = "配方编号")
        private String formulaCode;

        /**成本含税单价*/
        @Schema(description = "成本含税单价")
        private BigDecimal unitPriceIncludingTax;

        /**成本不含税单价*/
        @Schema(description = "成本不含税单价")
        private BigDecimal unitPriceExcludingTax;

        /**税率*/
        @Schema(description = "税率")
        private BigDecimal taxRate;

        /**含税总价*/
        @Schema(description = "含税总价")
        private BigDecimal totalIncludingTax;

        /**不含税总价*/
        @Schema(description = "不含税总价")
        private BigDecimal totalExcludingTax;
    }

    /**
     * 其他成本明细DTO
     */
    @Schema(description = "其他成本明细DTO")
    @Data
    public static class OtherCostDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**费用名称*/
        @Schema(description = "费用名称")
        private String expenseName;

        /**费用金额*/
        @Schema(description = "费用金额")
        private BigDecimal expenseAmount;

        /**备注*/
        @Schema(description = "备注")
        private String remark;
    }

    /**
     * 税金及附加明细DTO
     */
    @Schema(description = "税金及附加明细DTO")
    @Data
    public static class TaxCostDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**税种名称*/
        @Schema(description = "税种名称")
        private String taxName;

        /**税率*/
        @Schema(description = "税率")
        private BigDecimal taxRate;

        /**费用金额*/
        @Schema(description = "费用金额")
        private BigDecimal taxAmount;

        /**备注*/
        @Schema(description = "备注")
        private String remark;
    }

    /**
     * 原料明细汇总DTO
     */
    @Schema(description = "原料明细汇总DTO")
    @Data
    public static class MaterialSummaryDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**物料编码*/
        @Excel(name = "物料编码", width = 15)
        @Schema(description = "物料编码")
        private String materialCode;

        /**物料名称*/
        @Excel(name = "物料名称", width = 20)
        @Schema(description = "物料名称")
        private String materialName;

        /**用量*/
        @Excel(name = "用量", width = 15)
        @Schema(description = "用量")
        private BigDecimal usage;

        /**单位*/
        @Excel(name = "单位", width = 10)
        @Schema(description = "单位")
        private String unit;

        /**税率*/
        @Excel(name = "税率(%)", width = 12)
        @Schema(description = "税率")
        private BigDecimal taxRate;

        /**含税单价（万元）*/
        @Excel(name = "含税单价(万元)", width = 18)
        @Schema(description = "含税单价（万元）")
        private BigDecimal unitPriceIncludingTax;

        /**不含税单价（万元）*/
        @Excel(name = "不含税单价(万元)", width = 18)
        @Schema(description = "不含税单价（万元）")
        private BigDecimal unitPriceExcludingTax;

        /**含税总价（万元）*/
        @Excel(name = "含税总价(万元)", width = 18)
        @Schema(description = "含税总价（万元）")
        private BigDecimal totalPriceIncludingTax;

        /**不含税总价（万元）*/
        @Excel(name = "不含税总价(万元)", width = 18)
        @Schema(description = "不含税总价（万元）")
        private BigDecimal totalPriceExcludingTax;
    }
}
