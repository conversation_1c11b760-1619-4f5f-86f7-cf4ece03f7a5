package com.cdkit.modules.cm.application.budget;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import com.cdkit.modules.cm.domain.budget.service.AnnualBudgetDetailDomainService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 年度预算明细应用服务
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AnnualBudgetDetailApplication {

    private final AnnualBudgetDetailDomainService annualBudgetDetailDomainService;

    /**
     * 保存年度预算明细数据
     * 
     * @param budgetId 年度预算主表ID
     * @param budgetDetailList 明细数据列表
     */
    public void saveBudgetDetails(String budgetId, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        log.info("开始保存年度预算明细数据，budgetId: {}", budgetId);
        
        // 调用领域服务进行业务验证
        annualBudgetDetailDomainService.saveBudgetDetails(budgetId, budgetDetailList);

        // 记录明细数据信息（实际保存逻辑需要在基础设施层实现）
        if (budgetDetailList != null && !budgetDetailList.isEmpty()) {
            log.info("明细数据验证通过，共 {} 条记录", budgetDetailList.size());

            for (CostAnnualBudgetEntity.BudgetDetailInfo detailInfo : budgetDetailList) {
                log.info("明细记录：项目编号={}, 项目名称={}, 收入预算={}, 直接成本预算={}",
                        detailInfo.getProjectCode(),
                        detailInfo.getProjectName(),
                        detailInfo.getRevenueBudget(),
                        detailInfo.getDirectCostBudget());

                if (detailInfo.getDirectCostList() != null) {
                    for (CostAnnualBudgetEntity.DirectCostInfo directCostInfo : detailInfo.getDirectCostList()) {
                        log.info("  直接成本明细：科目={}, 释义={}, 金额={}",
                                directCostInfo.getCostItem(),
                                directCostInfo.getCostDescription(),
                                directCostInfo.getCostAmount());
                    }
                }
            }

            // TODO: 实际的数据库保存逻辑需要在基础设施层实现
            log.warn("注意：明细数据验证通过，但实际保存到数据库的逻辑尚未完全实现");
        }
        
        log.info("年度预算明细数据保存完成，budgetId: {}", budgetId);
    }
}
