package com.cdkit.modules.cm.application.budget;

import com.cdkit.modules.cm.domain.budget.event.BudgetDetailSaveEvent;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * 年度预算明细保存服务
 * 处理明细数据的关联关系和保存逻辑
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AnnualBudgetDetailSaveService {

    private final ApplicationEventPublisher eventPublisher;

    /**
     * 保存年度预算明细数据（包含关联关系处理）
     * 
     * @param budgetId 年度预算主表ID
     * @param budgetDetailList 明细数据列表
     */
    public void saveDetailsWithRelations(String budgetId, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        if (budgetDetailList == null || budgetDetailList.isEmpty()) {
            log.info("没有明细数据需要保存，budgetId: {}", budgetId);
            return;
        }

        log.info("开始保存年度预算明细数据（含关联关系），budgetId: {}, 明细数量: {}", budgetId, budgetDetailList.size());

        try {
            for (CostAnnualBudgetEntity.BudgetDetailInfo detailInfo : budgetDetailList) {
                // 设置关联的预算主表ID
                detailInfo.setBudgetId(budgetId);
                
                // 如果明细ID为空，生成新的ID
                if (detailInfo.getId() == null || detailInfo.getId().trim().isEmpty()) {
                    detailInfo.setId(UUID.randomUUID().toString());
                }
                
                log.info("处理明细记录：");
                log.info("  - 明细ID: {}", detailInfo.getId());
                log.info("  - 关联预算主表ID: {}", detailInfo.getBudgetId());
                log.info("  - 项目编号: {}", detailInfo.getProjectCode());
                log.info("  - 项目名称: {}", detailInfo.getProjectName());
                log.info("  - 收入预算: {}", detailInfo.getRevenueBudget());
                log.info("  - 直接成本预算: {}", detailInfo.getDirectCostBudget());
                
                // 处理直接成本明细的关联关系
                if (detailInfo.getDirectCostList() != null && !detailInfo.getDirectCostList().isEmpty()) {
                    log.info("  - 直接成本明细数量: {}", detailInfo.getDirectCostList().size());
                    
                    for (int i = 0; i < detailInfo.getDirectCostList().size(); i++) {
                        CostAnnualBudgetEntity.DirectCostInfo directCostInfo = detailInfo.getDirectCostList().get(i);
                        
                        // 设置关联的明细ID
                        directCostInfo.setBudgetDetailId(detailInfo.getId());
                        
                        // 如果直接成本ID为空，生成新的ID
                        if (directCostInfo.getId() == null || directCostInfo.getId().trim().isEmpty()) {
                            directCostInfo.setId(UUID.randomUUID().toString());
                        }
                        
                        log.info("    直接成本明细[{}]：", i + 1);
                        log.info("      - 直接成本ID: {}", directCostInfo.getId());
                        log.info("      - 关联明细ID: {}", directCostInfo.getBudgetDetailId());
                        log.info("      - 费用科目: {}", directCostInfo.getCostItem());
                        log.info("      - 科目释义: {}", directCostInfo.getCostDescription());
                        log.info("      - 预算金额: {}", directCostInfo.getCostAmount());
                    }
                }
                
                log.info("明细记录关联关系处理完成");
            }

            // 发布事件，让基础设施层监听并处理实际的数据库保存
            BudgetDetailSaveEvent event = new BudgetDetailSaveEvent(this, budgetId, budgetDetailList);
            eventPublisher.publishEvent(event);

            log.info("明细数据关联关系处理完成，已发布保存事件");

        } catch (Exception e) {
            log.error("保存年度预算明细数据失败，budgetId: {}", budgetId, e);
            throw new RuntimeException("保存明细数据失败：" + e.getMessage(), e);
        }

        log.info("年度预算明细数据保存完成，budgetId: {}", budgetId);
    }

    /**
     * 验证关联关系的完整性
     */
    private void validateRelations(String budgetId, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        for (CostAnnualBudgetEntity.BudgetDetailInfo detailInfo : budgetDetailList) {
            if (detailInfo.getBudgetId() == null || !detailInfo.getBudgetId().equals(budgetId)) {
                throw new IllegalArgumentException("明细记录的预算主表ID不匹配");
            }

            if (detailInfo.getDirectCostList() != null) {
                for (CostAnnualBudgetEntity.DirectCostInfo directCostInfo : detailInfo.getDirectCostList()) {
                    if (directCostInfo.getBudgetDetailId() == null || !directCostInfo.getBudgetDetailId().equals(detailInfo.getId())) {
                        throw new IllegalArgumentException("直接成本明细的预算明细ID不匹配");
                    }
                }
            }
        }
    }
}
