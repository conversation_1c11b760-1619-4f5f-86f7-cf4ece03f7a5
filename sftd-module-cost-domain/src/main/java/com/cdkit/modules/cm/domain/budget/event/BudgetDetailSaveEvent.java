package com.cdkit.modules.cm.domain.budget.event;

import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * 年度预算明细保存事件
 * 通过Spring事件机制实现应用层和基础设施层的解耦
 * <AUTHOR>
 * @date 2025-08-04
 */
@Getter
public class BudgetDetailSaveEvent extends ApplicationEvent {

    private final String budgetId;
    private final List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList;

    public BudgetDetailSaveEvent(Object source, String budgetId, List<CostAnnualBudgetEntity.BudgetDetailInfo> budgetDetailList) {
        super(source);
        this.budgetId = budgetId;
        this.budgetDetailList = budgetDetailList;
    }
}
