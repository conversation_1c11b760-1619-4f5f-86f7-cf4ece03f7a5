package com.cdkit.modules.cm.api.project;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.api.project.dto.CostProjectPlanDTO;
import com.cdkit.modules.cm.api.project.dto.CostProjectPlanAddRequest;
import com.cdkit.modules.cm.api.project.dto.CostProjectPlanEditRequest;
import com.cdkit.modules.cm.api.project.dto.CostProjectPlanCalculateResponse;
import com.cdkit.modules.cm.api.project.dto.CostProjectPlanCalculateRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

/**
 * 项目计划管理API
 * <AUTHOR>
 * @date 2025/07/18
 */
public interface IProjectPlanApi {

    @GetMapping(value = "/list")
    @Operation(summary = "分页查询项目计划列表", description = "分页查询项目计划列表")
    Result<IPage<CostProjectPlanDTO>> queryPageList(
            @RequestParam(name="planCode", required=false) String planCode,
            @RequestParam(name="planName", required=false) String planName,
            @RequestParam(name="projectPlanStatus", required=false) String projectPlanStatus,
            @RequestParam(name="projectCode", required=false) String projectCode,
            @RequestParam(name="projectName", required=false) String projectName,
            @RequestParam(name="planType", required=false) String planType,
            @RequestParam(name="center", required=false) String center,
            @RequestParam(name="projectGroup", required=false) String projectGroup,
            @RequestParam(name="annualBudgetId", required=false) String annualBudgetId,
            @RequestParam(name="projectType", required=false) String projectType,
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize);

    @PostMapping(value = "/add")
    @Operation(summary = "新增项目计划", description = "新增项目计划")
    Result<String> add(@Valid @RequestBody CostProjectPlanAddRequest request);

    @PutMapping(value = "/edit")
    @Operation(summary = "编辑项目计划", description = "编辑项目计划")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    Result<String> edit(@Valid @RequestBody CostProjectPlanEditRequest request);

    @DeleteMapping(value = "/delete")
    @Operation(summary = "删除项目计划", description = "删除项目计划")
    Result<String> delete(@RequestParam(name="id", required=true) String id);

    @DeleteMapping(value = "/deleteBatch")
    @Operation(summary = "批量删除项目计划", description = "批量删除项目计划")
    Result<String> deleteBatch(@RequestParam(name="ids", required=true) String ids);

    @GetMapping(value = "/queryById")
    @Operation(summary = "根据ID查询项目计划", description = "根据ID查询项目计划")
    Result<CostProjectPlanDTO> queryById(@RequestParam(name="id", required=true) String id);

    @PostMapping(value = "/calculate")
    @Operation(summary = "计算项目计划预算依据", description = "根据项目计划明细数据计算预算依据，包括成本总计、项目利润、利润率等")
    Result<CostProjectPlanCalculateResponse> calculateBudgetBasis(
            @Parameter(description = "项目计划数据", required = true) @RequestBody CostProjectPlanCalculateRequest request);

    @PostMapping(value = "/calculate/{planId}")
    @Operation(summary = "计算已保存项目计划预算依据", description = "计算已保存的项目计划预算依据，只计算不保存")
    Result<CostProjectPlanCalculateResponse> calculateBudgetBasis(
            @Parameter(description = "项目计划ID", required = true) @PathVariable String planId);

    @GetMapping(value = "/canSubmit/{planId}")
    @Operation(summary = "检查是否可以提交审批", description = "检查项目计划是否满足提交审批的条件")
    Result<Boolean> canSubmitForApproval(
            @Parameter(description = "项目计划ID", required = true) @PathVariable String planId);

    @PostMapping(value = "/submit/{planId}")
    @Operation(summary = "提交项目计划审批", description = "提交项目计划到F2审核系统进行审批，会自动传递租户信息")
    Result<String> submitForApproval(
            @Parameter(description = "项目计划ID", required = true) @PathVariable String planId,
            HttpServletRequest request);

    @RequestMapping(value = "/exportXls")
    @Operation(summary = "计算导出原料明细", description = "计算导出原料明细")
    ModelAndView exportXls(@Parameter(description = "项目计划数据", required = true) @RequestBody CostProjectPlanCalculateRequest requestO);

}
