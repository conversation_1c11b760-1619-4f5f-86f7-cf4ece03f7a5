package com.cdkit.modules.cm;

import com.cdkit.modules.cm.application.project.ProjectApplication;
import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectEntity;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

/**
 * 测试空数据导出功能
 * <AUTHOR>
 * @date 2025/07/15
 */
@SpringBootTest
@Slf4j
public class ExportEmptyDataTest {

    @Autowired
    private ProjectApplication projectApplication;

    @Test
    public void testExportEmptyData() {
        try {
            // 创建一个不存在的查询条件
            CostProjectEntity queryEntity = new CostProjectEntity();
            queryEntity.setProjectName("不存在的项目名称_测试");
            
            // 获取导出数据
            List<CostProjectEntity> exportList = projectApplication.getExportList(queryEntity);
            
            // 验证结果
            log.info("导出数据测试结果: 共{}条数据", exportList.size());
            
            // 即使没有数据，也应该返回空列表而不是null
            assert exportList != null : "导出列表不应该为null";
            log.info("✅ 空数据导出测试通过：返回了空列表而不是null");
            
        } catch (Exception e) {
            log.error("空数据导出测试失败", e);
            throw e;
        }
    }

    @Test
    public void testExportWithNullQuery() {
        try {
            // 测试null查询条件
            List<CostProjectEntity> exportList = projectApplication.getExportList(null);

            // 验证结果
            log.info("null查询条件测试结果: 共{}条数据", exportList.size());

            // 应该返回空列表而不是抛出异常
            assert exportList != null : "导出列表不应该为null";
            log.info("✅ null查询条件测试通过：返回了空列表而不是抛出异常");

        } catch (Exception e) {
            log.error("null查询条件测试失败", e);
            throw e;
        }
    }

    @Test
    public void testExportMainTableOnly() {
        try {
            // 测试只导出主表数据
            CostProjectEntity queryEntity = new CostProjectEntity();
            List<CostProjectEntity> exportList = projectApplication.getMainTableExportList(queryEntity);

            // 验证结果
            log.info("主表导出测试结果: 共{}条数据", exportList.size());

            // 验证不包含子表数据
            for (CostProjectEntity project : exportList) {
                assert project.getCostProjectWorkloadList() == null : "主表导出不应该包含子表数据";
            }

            log.info("✅ 主表导出测试通过：只包含主表数据，不包含子表数据");

        } catch (Exception e) {
            log.error("主表导出测试失败", e);
            throw e;
        }
    }
}
