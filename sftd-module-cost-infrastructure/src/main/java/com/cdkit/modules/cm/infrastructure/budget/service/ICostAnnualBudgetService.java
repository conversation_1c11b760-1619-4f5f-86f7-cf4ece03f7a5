package com.cdkit.modules.cm.infrastructure.budget.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.budget.entity.*;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 年度总预算
 * @Author: cdkit-boot
 * @Date:   2025-07-30
 * @Version: V1.0
 */
public interface ICostAnnualBudgetService extends IService<CostAnnualBudget> {

	/**
	 * 添加一对多
	 *
	 * @param costAnnualBudget
	 * @param costAnnualBudgetDetailList
	 * @param costAnnualBudgetCenterIndirectCostList
	 * @param costAnnualBudgetNonOperatingIndirectCostList
	 * @param costAnnualBudgetComprehensiveIndirectCostList
	 */
	public void saveMain(CostAnnualBudget costAnnualBudget, List<CostAnnualBudgetDetail> costAnnualBudgetDetailList, List<CostAnnualBudgetCenterIndirectCost> costAnnualBudgetCenterIndirectCostList, List<CostAnnualBudgetNonOperatingIndirectCost> costAnnualBudgetNonOperatingIndirectCostList, List<CostAnnualBudgetComprehensiveIndirectCost> costAnnualBudgetComprehensiveIndirectCostList) ;
	
	/**
	 * 修改一对多
	 *
   * @param costAnnualBudget
   * @param costAnnualBudgetDetailList
   * @param costAnnualBudgetCenterIndirectCostList
   * @param costAnnualBudgetNonOperatingIndirectCostList
   * @param costAnnualBudgetComprehensiveIndirectCostList
	 */
	public void updateMain(CostAnnualBudget costAnnualBudget,List<CostAnnualBudgetDetail> costAnnualBudgetDetailList,List<CostAnnualBudgetCenterIndirectCost> costAnnualBudgetCenterIndirectCostList,List<CostAnnualBudgetNonOperatingIndirectCost> costAnnualBudgetNonOperatingIndirectCostList,List<CostAnnualBudgetComprehensiveIndirectCost> costAnnualBudgetComprehensiveIndirectCostList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);
	
}
