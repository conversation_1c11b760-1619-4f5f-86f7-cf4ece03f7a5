package com.cdkit.modules.cm.domain.budget.mode.entity;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 年度总预算领域实体
 * <AUTHOR>
 * @date 2025-07-30
 */
@Data
public class CostAnnualBudgetEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**UUID主键*/
    private String id;

    /**总预算编号(ZYS+4位年份+3位流水)*/
    private String budgetCode;

    /**总预算名称*/
    private String budgetName;

    /**年份*/
    private String budgetYear;

    /**版本号*/
    private String version;

    /**状态(PENDING_LOCK-待锁定/APPROVING-审批中/LOCKED-已锁定/CHANGED-已变更)*/
    private String budgetStatus;

    /**所属单位*/
    private String professionalCompany;

    /**收入总金额(万元)*/
    private BigDecimal revenueTotalAmount;

    /**收入已使用金额(万元)*/
    private BigDecimal revenueUsedAmount;

    /**收入剩余金额(万元)*/
    private BigDecimal revenueRemainingAmount;

    /**收入总金额差异额(万元)*/
    private BigDecimal revenueDifferenceAmount;

    /**直接成本总金额(万元)*/
    private BigDecimal directCostTotalAmount;

    /**直接成本已使用金额(万元)*/
    private BigDecimal directCostUsedAmount;

    /**直接成本剩余金额(万元)*/
    private BigDecimal directCostRemainingAmount;

    /**直接成本总金额差异额(万元)*/
    private BigDecimal directCostDifferenceAmount;

    /**备注*/
    private String remark;

    /**父预算ID(变更时关联原预算)*/
    private String parentBudgetId;

    /**变更原因*/
    private String changeReason;

    /**附件URL*/
    private String attachmentUrl;

    /**创建时间*/
    private Date createTime;

    /**创建人*/
    private String createBy;

    /**更新时间*/
    private Date updateTime;

    /**更新人*/
    private String updateBy;

    /**租户ID*/
    private String tenantId;

    /**所属部门代码*/
    private String sysOrgCode;

    /**
     * 计算收入剩余金额
     * 收入剩余金额 = 收入总金额 - 收入已使用金额
     */
    public void calculateRevenueRemainingAmount() {
        if (revenueTotalAmount != null && revenueUsedAmount != null) {
            this.revenueRemainingAmount = revenueTotalAmount.subtract(revenueUsedAmount);
        }
    }

    /**
     * 计算直接成本剩余金额
     * 直接成本剩余金额 = 直接成本总金额 - 直接成本已使用金额
     */
    public void calculateDirectCostRemainingAmount() {
        if (directCostTotalAmount != null && directCostUsedAmount != null) {
            this.directCostRemainingAmount = directCostTotalAmount.subtract(directCostUsedAmount);
        }
    }

    /**
     * 验证预算状态是否可以修改
     *
     * @return true-可以修改，false-不可修改
     */
    public boolean canModify() {
        return "PENDING_LOCK".equals(budgetStatus) || "CHANGED".equals(budgetStatus);
    }

    /**
     * 验证预算状态是否可以删除
     *
     * @return true-可以删除，false-不可删除
     */
    public boolean canDelete() {
        return "PENDING_LOCK".equals(budgetStatus);
    }

    /**
     * 项目年度预算明细信息
     */
    @Data
    public static class BudgetDetailInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**UUID主键*/
        private String id;

        /**关联项目年度计划ID*/
        private String planId;

        /**项目编号*/
        private String projectCode;

        /**年度预算项目名称*/
        private String projectName;

        /**所属单位*/
        private String professionalCompany;

        /**下属中心*/
        private String center;

        /**预算类型*/
        private String budgetType;

        /**WBS编号*/
        private String wbsCode;

        /**项目类型*/
        private String projectType;

        /**四级业务*/
        private String fourthLevelBusiness;

        /**业务小类*/
        private String businessSubcategory;

        /**收入预算(万元)*/
        private BigDecimal revenueBudget;

        /**直接成本预算(万元)*/
        private BigDecimal directCostBudget;

        /**其他成本预算(万元)*/
        private BigDecimal otherCostBudget;

        /**利润预算(万元)*/
        private BigDecimal profitBudget;

        /**直接成本明细列表*/
        private List<DirectCostInfo> directCostList;
    }

    /**
     * 直接成本明细信息
     */
    @Data
    public static class DirectCostInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**UUID主键*/
        private String id;

        /**费用科目*/
        private String costItem;

        /**科目释义*/
        private String costDescription;

        /**支出预算金额(万元)*/
        private BigDecimal costAmount;
    }
}
