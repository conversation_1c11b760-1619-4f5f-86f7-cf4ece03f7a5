package com.cdkit.modules.cm.domain.project.service;

import com.cdkit.modules.cm.domain.project.mode.entity.CostProjectPlanEntity;
import com.cdkit.modules.cm.domain.project.mode.entity.CostDirectCostEntity;
import com.cdkit.modules.cm.domain.project.valobj.MaterialSummaryVO;
import com.cdkit.modules.cm.domain.project.repository.CostProjectPlanRepository;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.ArrayList;

/**
 * 项目计划领域服务
 * 处理项目计划相关的业务逻辑
 *
 * <AUTHOR>
 * @date 2025/07/18
 */
@Slf4j
@Service
public class CostProjectPlanDomainService {

    private final CostProjectPlanRepository costProjectPlanRepository;

    public CostProjectPlanDomainService(CostProjectPlanRepository costProjectPlanRepository) {
        this.costProjectPlanRepository = costProjectPlanRepository;
    }

    /**
     * 计算项目计划预算依据（只计算不保存）
     *
     * @param planId 项目计划ID
     * @return 计算后的项目计划实体
     */
    public CostProjectPlanEntity calculateBudgetBasisOnly(String planId) {
        // 查询项目计划详情（包含所有子表数据）
        CostProjectPlanEntity projectPlan = costProjectPlanRepository.queryByIdWithDetails(planId);

        if (projectPlan == null) {
            throw new IllegalArgumentException("项目计划不存在，ID: " + planId);
        }

        // 检查是否可以计算
        if (!projectPlan.canCalculate()) {
            throw new IllegalStateException("项目计划不满足计算条件");
        }

        // 注意：直接成本明细应该在应用层计算并设置，这里只执行基础计算

        // 执行计算（不保存到数据库）
        projectPlan.calculateBudgetBasis();

        return projectPlan;
    }

    /**
     * 计算项目计划预算依据并保存
     *
     * @param planId 项目计划ID
     * @return 计算后的项目计划实体
     */
    public CostProjectPlanEntity calculateBudgetBasisAndSave(String planId) {
        // 查询项目计划详情（包含所有子表数据）
        CostProjectPlanEntity projectPlan = costProjectPlanRepository.queryByIdWithDetails(planId);

        if (projectPlan == null) {
            throw new IllegalArgumentException("项目计划不存在，ID: " + planId);
        }

        // 检查是否可以计算
        if (!projectPlan.canCalculate()) {
            throw new IllegalStateException("项目计划不满足计算条件");
        }

        // 注意：直接成本明细应该在应用层计算并设置，这里只执行基础计算
        // 执行计算
        projectPlan.calculateBudgetBasis();

        // 保存计算结果
        costProjectPlanRepository.updateMain(
            projectPlan,
            projectPlan.getCostProjectPlanDetailList(),
            projectPlan.getCostDirectCostList(),
            projectPlan.getCostOtherCostList(),
            projectPlan.getCostTaxCostList(),
            projectPlan.getCostMaterialDetailList()
        );

        return projectPlan;
    }

    /**
     * 计算项目计划预算依据（兼容旧方法，建议使用 calculateBudgetBasisAndSave）
     *
     * @param planId 项目计划ID
     * @return 计算后的项目计划实体
     */
    @Deprecated
    public CostProjectPlanEntity calculateBudgetBasis(String planId) {
        return calculateBudgetBasisAndSave(planId);
    }

    /**
     * 生成原料明细汇总
     * 
     * @param projectPlan 项目计划实体
     * @return 原料明细汇总列表
     */
    public List<MaterialSummaryVO> generateMaterialSummary(CostProjectPlanEntity projectPlan) {
        List<MaterialSummaryVO> materialSummaryList = new ArrayList<>();

        if (projectPlan.getCostDirectCostList() == null || projectPlan.getCostDirectCostList().isEmpty()) {
            return materialSummaryList;
        }

        // 按物料编码分组汇总
        Map<String, List<CostDirectCostEntity>> groupedByFormula = projectPlan.getCostDirectCostList()
            .stream()
            .filter(cost -> cost.getFormulaCode() != null && !cost.getFormulaCode().trim().isEmpty())
            .collect(Collectors.groupingBy(CostDirectCostEntity::getFormulaCode));

        for (Map.Entry<String, List<CostDirectCostEntity>> entry : groupedByFormula.entrySet()) {
            String formulaCode = entry.getKey();
            List<CostDirectCostEntity> costs = entry.getValue();

            // 取第一个作为代表获取基本信息
            CostDirectCostEntity representative = costs.get(0);

            // 计算汇总数据
            BigDecimal totalUsage = costs.stream()
                .filter(cost -> cost.getEstimatedUsage() != null)
                .map(CostDirectCostEntity::getEstimatedUsage)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalPriceIncludingTax = costs.stream()
                .filter(cost -> cost.getTotalIncludingTax() != null)
                .map(CostDirectCostEntity::getTotalIncludingTax)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal totalPriceExcludingTax = costs.stream()
                .filter(cost -> cost.getTotalExcludingTax() != null)
                .map(CostDirectCostEntity::getTotalExcludingTax)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 计算平均单价
            BigDecimal avgUnitPriceIncludingTax = BigDecimal.ZERO;
            BigDecimal avgUnitPriceExcludingTax = BigDecimal.ZERO;
            if (totalUsage.compareTo(BigDecimal.ZERO) > 0) {
                avgUnitPriceIncludingTax = totalPriceIncludingTax.divide(totalUsage, 4, RoundingMode.HALF_UP);
                avgUnitPriceExcludingTax = totalPriceExcludingTax.divide(totalUsage, 4, RoundingMode.HALF_UP);
            }

            MaterialSummaryVO summary = MaterialSummaryVO.builder()
                .materialCode(formulaCode)
                .materialName(representative.getFormulaName())
                .usage(totalUsage)
                .unit("吨") // 默认单位
                .taxRate(representative.getTaxRate())
                .unitPriceIncludingTax(avgUnitPriceIncludingTax)
                .unitPriceExcludingTax(avgUnitPriceExcludingTax)
                .totalPriceIncludingTax(totalPriceIncludingTax)
                .totalPriceExcludingTax(totalPriceExcludingTax)
                .productModel(representative.getProductName())
                .formulaCode(formulaCode)
                .formulaName(representative.getFormulaName())
                .build();

            if (summary.isValid()) {
                materialSummaryList.add(summary);
            }
        }

        return materialSummaryList;
    }



    /**
     * 计算成本含税单价
     * 成本含税单价 = 物料价格 / 产成品AP-10数量
     *
     * @param materialPrice 物料价格
     * @param ap10Quantity AP-10产品数量
     * @return 成本含税单价
     */
    public BigDecimal calculateCostUnitPriceIncludingTax(BigDecimal materialPrice, BigDecimal ap10Quantity) {
        if (materialPrice == null || ap10Quantity == null || ap10Quantity.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        return materialPrice.divide(ap10Quantity, 4, RoundingMode.HALF_UP);
    }

    /**
     * 验证项目计划数据完整性
     * 
     * @param projectPlan 项目计划实体
     * @return 验证结果消息列表
     */
    public List<String> validateProjectPlanData(CostProjectPlanEntity projectPlan) {
        List<String> messages = new ArrayList<>();

        if (projectPlan == null) {
            messages.add("项目计划不能为空");
            return messages;
        }

        // 验证基本信息
        if (projectPlan.getPlanName() == null || projectPlan.getPlanName().trim().isEmpty()) {
            messages.add("计划名称不能为空");
        }

        if (projectPlan.getProjectName() == null || projectPlan.getProjectName().trim().isEmpty()) {
            messages.add("项目名称不能为空");
        }

        // 验证明细数据
        if (projectPlan.getCostProjectPlanDetailList() == null || projectPlan.getCostProjectPlanDetailList().isEmpty()) {
            messages.add("项目计划明细不能为空");
        } else {
            // 验证明细数据的完整性
            for (int i = 0; i < projectPlan.getCostProjectPlanDetailList().size(); i++) {
                var detail = projectPlan.getCostProjectPlanDetailList().get(i);
                String prefix = "明细第" + (i + 1) + "行：";
                
                if (detail.getProductModel() == null || detail.getProductModel().trim().isEmpty()) {
                    messages.add(prefix + "产品型号不能为空");
                }
                
                if (detail.getDensity() == null || detail.getDensity().compareTo(BigDecimal.ZERO) <= 0) {
                    messages.add(prefix + "密度必须大于0");
                }
                
                if (detail.getUsageAmount() == null || detail.getUsageAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    messages.add(prefix + "用量必须大于0");
                }
            }
        }

        return messages;
    }

    /**
     * 检查项目计划是否可以提交审批
     * 
     * @param projectPlan 项目计划实体
     * @return true-可以提交，false-不可以提交
     */
    public boolean canSubmitForApproval(CostProjectPlanEntity projectPlan) {
        if (projectPlan == null) {
            return false;
        }

        // 状态必须是待提交
        if (!"PENDING_SUBMIT".equals(projectPlan.getProjectPlanStatus())) {
            return false;
        }

        // 数据验证必须通过
        List<String> validationMessages = validateProjectPlanData(projectPlan);
        if (!validationMessages.isEmpty()) {
            return false;
        }

        // 必须已经计算过预算依据
        return projectPlan.getCostTotal() != null && projectPlan.getCostTotal().compareTo(BigDecimal.ZERO) > 0;
    }
}
