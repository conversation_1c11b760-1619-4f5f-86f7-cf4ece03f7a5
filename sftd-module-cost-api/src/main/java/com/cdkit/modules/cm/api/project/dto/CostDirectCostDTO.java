package com.cdkit.modules.cm.api.project.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 直接成本明细DTO
 * @Author: cdkit-boot
 * @Date: 2025-07-18
 * @Version: V1.0
 */
@Schema(description = "直接成本明细DTO")
@Data
public class CostDirectCostDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**UUID主键*/
    @Schema(description = "UUID主键")
    private String id;

    /**关联计划ID*/
    @Schema(description = "关联计划ID")
    private String planId;

    /**产品名称*/
    @Schema(description = "产品名称")
    private String productName;

    /**预计用量(吨)*/
    @Schema(description = "预计用量(吨)")
    private BigDecimal estimatedUsage;

    /**配方名称*/
    @Schema(description = "配方名称")
    private String formulaName;

    /**配方编号*/
    @Schema(description = "配方编号")
    private String formulaCode;

    /**材料成本含税单价(万元)*/
    @Schema(description = "材料成本含税单价(万元)")
    private BigDecimal unitPriceIncludingTax;

    /**材料成本不含税单价(万元)*/
    @Schema(description = "材料成本不含税单价(万元)")
    private BigDecimal unitPriceExcludingTax;

    /**税率(%)*/
    @Schema(description = "税率(%)")
    private BigDecimal taxRate;

    /**材料成本含税总价(万元)*/
    @Schema(description = "材料成本含税总价(万元)")
    private BigDecimal totalIncludingTax;

    /**材料成本不含税总价(万元)*/
    @Schema(description = "材料成本不含税总价(万元)")
    private BigDecimal totalExcludingTax;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**创建人*/
    @Schema(description = "创建人")
    private String createBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
}
