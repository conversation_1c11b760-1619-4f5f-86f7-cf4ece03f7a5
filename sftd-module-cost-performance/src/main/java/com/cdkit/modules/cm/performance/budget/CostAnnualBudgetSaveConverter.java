package com.cdkit.modules.cm.performance.budget;

import com.cdkit.modules.cm.api.budget.request.CostAnnualBudgetSaveRequest;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostAnnualBudgetEntity;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 年度预算保存请求转换器
 * <AUTHOR>
 * @date 2025-08-04
 */
public class CostAnnualBudgetSaveConverter {

    /**
     * 保存请求转换为领域实体
     *
     * @param request 保存请求对象
     * @return 领域实体
     */
    public static CostAnnualBudgetEntity toEntity(CostAnnualBudgetSaveRequest request) {
        if (request == null) {
            return null;
        }
        
        CostAnnualBudgetEntity entity = new CostAnnualBudgetEntity();
        BeanUtils.copyProperties(request, entity);
        return entity;
    }

    /**
     * 保存请求中的明细列表转换为领域实体明细列表
     *
     * @param detailRequestList 明细请求列表
     * @return 领域实体明细列表
     */
    public static List<CostAnnualBudgetEntity.BudgetDetailInfo> toBudgetDetailInfoList(
            List<CostAnnualBudgetSaveRequest.CostAnnualBudgetDetailRequest> detailRequestList) {
        if (detailRequestList == null || detailRequestList.isEmpty()) {
            return new ArrayList<>();
        }
        
        return detailRequestList.stream()
                .map(CostAnnualBudgetSaveConverter::toBudgetDetailInfo)
                .collect(Collectors.toList());
    }

    /**
     * 明细请求转换为领域实体明细
     *
     * @param detailRequest 明细请求对象
     * @return 领域实体明细
     */
    public static CostAnnualBudgetEntity.BudgetDetailInfo toBudgetDetailInfo(
            CostAnnualBudgetSaveRequest.CostAnnualBudgetDetailRequest detailRequest) {
        if (detailRequest == null) {
            return null;
        }
        
        CostAnnualBudgetEntity.BudgetDetailInfo detailInfo = new CostAnnualBudgetEntity.BudgetDetailInfo();
        BeanUtils.copyProperties(detailRequest, detailInfo);
        
        // 转换直接成本明细列表
        if (detailRequest.getDirectCostList() != null && !detailRequest.getDirectCostList().isEmpty()) {
            List<CostAnnualBudgetEntity.DirectCostInfo> directCostInfoList = detailRequest.getDirectCostList().stream()
                    .map(CostAnnualBudgetSaveConverter::toDirectCostInfo)
                    .collect(Collectors.toList());
            detailInfo.setDirectCostList(directCostInfoList);
        }
        
        return detailInfo;
    }

    /**
     * 直接成本请求转换为领域实体直接成本
     *
     * @param directCostRequest 直接成本请求对象
     * @return 领域实体直接成本
     */
    public static CostAnnualBudgetEntity.DirectCostInfo toDirectCostInfo(
            CostAnnualBudgetSaveRequest.CostAnnualBudgetDetailDirectCostRequest directCostRequest) {
        if (directCostRequest == null) {
            return null;
        }
        
        CostAnnualBudgetEntity.DirectCostInfo directCostInfo = new CostAnnualBudgetEntity.DirectCostInfo();
        BeanUtils.copyProperties(directCostRequest, directCostInfo);
        return directCostInfo;
    }
}
